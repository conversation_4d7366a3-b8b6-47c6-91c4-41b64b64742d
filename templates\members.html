{% extends "layout.html" %}
{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-bold text-[var(--theme-color)] mb-8 text-center">👥 قائمة المشتركين</h2>
    
    <!-- Success Message -->
    {% if request.args.get('success_message') %}
    <div class="alert bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6 animate-fade-in">{{ request.args.get('success_message') }}</div>
    {% endif %}

    <!-- Search Form -->
    <form method="GET" class="mb-8 bg-white p-6 rounded-lg shadow-lg">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label class="block text-gray-700 font-semibold mb-2">الاسم:</label>
                <input type="text" name="name" placeholder="الاسم" value="{{ request.args.get('name', '') }}" class="w-full">
            </div>
            <div>
                <label class="block text-gray-700 font-semibold mb-2">الهاتف:</label>
                <input type="text" name="phone" placeholder="الهاتف" value="{{ request.args.get('phone', '') }}" class="w-full">
            </div>
            <div>
                <label class="block text-gray-700 font-semibold mb-2">حالة الاشتراك:</label>
                <select name="status" class="w-full">
                    <option value="">🔄 حالة الاشتراك</option>
                    <option value="فعّال" {% if request.args.get('status') == 'فعّال' %}selected{% endif %}>فعّال</option>
                    <option value="قارب على الانتهاء" {% if request.args.get('status') == 'قارب على الانتهاء' %}selected{% endif %}>قارب على الانتهاء</option>
                    <option value="منتهي" {% if request.args.get('status') == 'منتهي' %}selected{% endif %}>منتهي</option>
                </select>
            </div>
            <div class="flex items-end">
                <button type="submit" class="w-full bg-[var(--theme-color)] hover:bg-[var(--theme-hover)] text-white font-semibold py-2.5 rounded-lg transition-transform duration-200 transform hover:-translate-y-1">🔍 بحث</button>
            </div>
        </div>
    </form>

    <!-- Members Table -->
    <div class="overflow-x-auto">
        <table class="w-full border-collapse bg-white rounded-lg shadow-lg">
            <thead>
                <tr class="bg-[var(--theme-color)] text-white">
                    <th class="p-4">الاسم</th>
                    <th class="p-4">الهاتف</th>
                    <th class="p-4">النوع</th>
                    <th class="p-4">نوع الاشتراك</th>
                    <th class="p-4">البداية</th>
                    <th class="p-4">النهاية</th>
                    <th class="p-4">قيمة الاشتراك</th>
                    <th class="p-4">المدفوع</th>
                    <th class="p-4">المتبقي</th>
                    <th class="p-4">الحالة</th>
                    <th class="p-4">الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for m in members %}
                <tr class="hover:bg-gray-50 transition-colors duration-200">
                    <td class="p-4">{{ m.Name }}</td>
                    <td class="p-4">{{ m.Phone }}</td>
                    <td class="p-4">{{ m.Gender }}</td>
                    <td class="p-4">{{ m.SubscriptionType }}</td>
                    <td class="p-4">{{ m.StartDate }}</td>
                    <td class="p-4">{{ m.EndDate }}</td>
                    <td class="p-4">{{ m.SubscriptionValue }}</td>
                    <td class="p-4">{{ m.PaidAmount }}</td>
                    <td class="p-4">{{ m.RemainingAmount }}</td>
                    <td class="p-4">{{ m.status }}</td>
                    <td class="p-4 flex space-x-2 space-x-reverse">
                        <a href="{{ url_for('edit_member', member_id=m.ID) }}" class="text-[var(--theme-color)] hover:text-[var(--theme-hover)]">✏️</a>
                        <a href="{{ url_for('delete_member', member_id=m.ID) }}" class="text-red-600 hover:text-red-800" onclick="return confirm('هل أنت متأكد من حذف هذا المشترك؟')">🗑️</a>
                        <a href="{{ url_for('generate_member_qr', member_id=m.ID) }}" class="text-[var(--theme-color)] hover:text-[var(--theme-hover)]">📷 QR</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}