/* Import Noto Sans Arabic for better Arabic typography */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');

/* Ensure RTL support */
html, body {
    direction: rtl;
    font-family: 'Noto Sans Arabic', sans-serif;
}

/* Table styles */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1.5rem;
    background-color: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

th, td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #e5e7eb;
}

th {
    background-color: var(--theme-color);
    color: white;
    font-weight: 700;
}

td {
    background-color: white;
}

tr:nth-child(even) {
    background-color: #f9fafb;
}

tr:hover {
    background-color: #f1f5f9;
}

/* Button styles */
button, input[type="submit"] {
    background-color: var(--theme-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

button:hover, input[type="submit"]:hover {
    background-color: var(--theme-hover);
    transform: translateY(-2px);
}

/* Form input styles */
input[type="text"], input[type="password"], input[type="number"], input[type="date"], select, input[type="color"] {
    width: 100%;
    padding: 0.75rem;
    margin-bottom: 1.25rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background-color: white;
    transition: border-color 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--theme-color);
    box-shadow: 0 0 0 3px rgba(var(--theme-color-rgb), 0.2);
}

/* Textarea styles */
textarea {
    width: 100%;
    padding: 0.75rem;
    margin-bottom: 1.25rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background-color: white;
    transition: border-color 0.3s ease;
}

/* Select2 styles */
.select2-container--default .select2-selection--single {
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    height: 46px;
    padding: 0.5rem;
    background-color: white;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 30px;
    color: #1f2937;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 40px;
}

.select2-container--default .select2-selection--single:focus {
    border-color: var(--theme-color);
    box-shadow: 0 0 0 3px rgba(var(--theme-color-rgb), 0.2);
}

/* Dashboard stat card styles */
.dashboard .stat {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard .stat:hover {
    transform: translateY(-4px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* Animation for alerts and messages */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* No results message */
p.no-results {
    font-size: 1.125rem;
    color: #4b5563;
    text-align: center;
    margin-top: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    table {
        font-size: 0.875rem;
    }
    th, td {
        padding: 0.5rem;
    }
    .dashboard {
        grid-template-columns: 1fr;
    }
    .dashboard .stat {
        padding: 1rem;
    }
    /* Adjust form layout for mobile */
    form .grid {
        grid-template-columns: 1fr;
    }
    /* Adjust product entry layout for mobile */
    .product-entry {
        grid-template-columns: 1fr;
    }
}