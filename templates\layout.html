<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% endblock %}</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <!-- Favicon -->
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <!-- Apply dynamic theme color -->
    <style>
        :root {
            --theme-color: {{ theme_color | default('#006699') }};
        }
    </style>
</head>
<body class="bg-gray-100 font-sans text-gray-800">
    <header class="bg-white shadow-lg p-4 flex items-center justify-between">
        <div class="flex items-center space-x-4 space-x-reverse">
            <img src="{{ logo_path | default(url_for('static', filename='logo.png')) }}" alt="Logo" class="w-12 h-12">
            <h1 class="text-2xl font-bold text-[var(--theme-color)]">{{ client_name | default('اسم العميل') }}</h1>
        </div>
        <nav class="bg-[var(--theme-color)] text-white rounded-lg p-2">
            <ul class="flex space-x-4 space-x-reverse">
                <li><a href="{{ url_for('home') }}" class="hover:bg-white hover:text-[var(--theme-color)] px-3 py-2 rounded transition">🏠 الرئيسية</a></li>
                {% if session.can_search %}
                <li><a href="{{ url_for('search') }}" class="hover:bg-white hover:text-[var(--theme-color)] px-3 py-2 rounded transition">🔍 البحث</a></li>
                {% endif %}
                {% if session.can_add_person %}
                <li><a href="{{ url_for('add_member') }}" class="hover:bg-white hover:text-[var(--theme-color)] px-3 py-2 rounded transition">➕ إضافة مشترك</a></li>
                {% endif %}
                <li><a href="{{ url_for('list_members') }}" class="hover:bg-white hover:text-[var(--theme-color)] px-3 py-2 rounded transition">👥 المشتركين</a></li>
                {% if session.role == 'admin' %}
                <li><a href="{{ url_for('manage_sales') }}" class="hover:bg-white hover:text-[var(--theme-color)] px-3 py-2 rounded transition">💰 المبيعات</a></li>
                <li><a href="{{ url_for('manage_products') }}" class="hover:bg-white hover:text-[var(--theme-color)] px-3 py-2 rounded transition">📦 المنتجات</a></li>
                <li><a href="{{ url_for('manage_attendance') }}" class="hover:bg-white hover:text-[var(--theme-color)] px-3 py-2 rounded transition">📅 الحضور</a></li>
                {% endif %}
                {% if session.can_reports %}
                <li><a href="{{ url_for('reports') }}" class="hover:bg-white hover:text-[var(--theme-color)] px-3 py-2 rounded transition">📋 التقارير</a></li>
                {% endif %}
                {% if session.role == 'admin' %}
                <li><a href="{{ url_for('inactive_members') }}" class="hover:bg-white hover:text-[var(--theme-color)] px-3 py-2 rounded transition">🚫 المشتركين غير النشطين</a></li>
                <li><a href="{{ url_for('inactive_people') }}" class="hover:bg-white hover:text-[var(--theme-color)] px-3 py-2 rounded transition">🚫 الإشتراكات المنتهية</a></li>
                <li><a href="{{ url_for('user_logs_filter') }}" class="hover:bg-white hover:text-[var(--theme-color)] px-3 py-2 rounded transition">🧾 سجل المستخدمين</a></li>
                <li><a href="{{ url_for('manage_users') }}" class="hover:bg-white hover:text-[var(--theme-color)] px-3 py-2 rounded transition">👤 إدارة المستخدمين</a></li>
                <li><a href="{{ url_for('system_settings') }}" class="hover:bg-white hover:text-[var(--theme-color)] px-3 py-2 rounded transition">⚙️ إعدادات النظام</a></li>
                {% endif %}
                <li><a href="{{ url_for('change_password') }}" class="hover:bg-white hover:text-[var(--theme-color)] px-3 py-2 rounded transition">🔑 تغيير كلمة المرور</a></li>
                <li><a href="{{ url_for('logout') }}" class="hover:bg-white hover:text-[var(--theme-color)] px-3 py-2 rounded transition">🚪 تسجيل الخروج</a></li>
            </ul>
        </nav>
    </header>
    <div class="container mx-auto px-4 py-6">
        {% if session.get('license_warning') %}
        <div class="alert bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-lg mb-4 animate-pulse">{{ session.get('license_warning') }}</div>
        {% endif %}
        {% for message in get_flashed_messages(with_categories=true) %}
        <div class="alert bg-{{ 'green' if message[0] == 'success' else 'red' }}-100 border border-{{ 'green' if message[0] == 'success' else 'red' }}-400 text-{{ 'green' if message[0] == 'success' else 'red' }}-700 px-4 py-3 rounded-lg mb-4 transition-opacity duration-500">{{ message[1] }}</div>
        {% endfor %}
        <main class="bg-white shadow-md rounded-lg p-6">
            {% block content %}{% endblock %}
        </main>
    </div>
    <footer class="bg-[var(--theme-color)] text-white p-4 mt-6">
        <div class="container mx-auto text-center">
            <p>تم التطوير بواسطة: {{ client_name | default('اسم العميل') }}</p>
            <p>تاريخ اليوم: {{ today }}</p>
            <p>المستخدم الحالي: {{ current_user | default('زائر') }}</p>
        </div>
    </footer>
</body>
</html>