<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الصالة الرياضية</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Noto Sans Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    <!-- Favicon -->
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}">

    <!-- Apply dynamic theme color -->
    <style>
        :root {
            --theme-color: {{ theme_color | default('#006699') }};
            --theme-hover: {{ theme_color | default('#004477') }};
        }

        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, var(--theme-color) 0%, #4f46e5 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
    </style>
</head>
<body>
    <!-- Login Container -->
    <div class="w-full max-w-md">
        <!-- Logo and Title -->
        <div class="text-center mb-8">
            <div class="mx-auto w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-lg mb-4">
                <img src="{{ logo_path | default(url_for('static', filename='logo.png')) }}"
                     alt="Logo" class="w-12 h-12 rounded-full object-cover">
            </div>
            <h1 class="text-2xl font-bold text-white mb-2">نظام إدارة الصالة الرياضية</h1>
            <p class="text-blue-100">مرحباً بك! يرجى تسجيل الدخول للمتابعة</p>
        </div>

        <!-- Login Form -->
        <div class="bg-white rounded-2xl shadow-2xl p-8">
            <!-- Error Messages -->
            {% with messages = get_flashed_messages(category_filter=['error']) %}
            {% if messages %}
                <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle text-red-500 ml-2"></i>
                        <div class="text-red-700">
                            {% for message in messages %}
                                {{ message }}
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endif %}
            {% endwith %}

            <form method="post" id="loginForm" class="space-y-6">
                <!-- Username Field -->
                <div class="form-group">
                    <label for="username" class="form-label flex items-center">
                        <i class="fas fa-user text-gray-400 ml-2"></i>
                        اسم المستخدم
                    </label>
                    <input type="text"
                           id="username"
                           name="username"
                           required
                           class="form-input"
                           placeholder="أدخل اسم المستخدم"
                           autocomplete="username">
                    <div class="form-error hidden" id="username-error">يرجى إدخال اسم المستخدم</div>
                </div>

                <!-- Password Field -->
                <div class="form-group">
                    <label for="password" class="form-label flex items-center">
                        <i class="fas fa-lock text-gray-400 ml-2"></i>
                        كلمة المرور
                    </label>
                    <div class="relative">
                        <input type="password"
                               id="password"
                               name="password"
                               required
                               class="form-input pl-10"
                               placeholder="أدخل كلمة المرور"
                               autocomplete="current-password">
                        <button type="button"
                                id="togglePassword"
                                class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                            <i class="fas fa-eye" id="eyeIcon"></i>
                        </button>
                    </div>
                    <div class="form-error hidden" id="password-error">يرجى إدخال كلمة المرور</div>
                </div>

                <!-- Remember Me -->
                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-[var(--theme-color)] focus:ring-[var(--theme-color)]">
                        <span class="mr-2 text-sm text-gray-600">تذكرني</span>
                    </label>
                    <a href="#" class="text-sm text-[var(--theme-color)] hover:underline">نسيت كلمة المرور؟</a>
                </div>

                <!-- Submit Button -->
                <button type="submit"
                        class="w-full bg-[var(--theme-color)] hover:bg-[var(--theme-hover)] text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-[var(--theme-color)] focus:ring-opacity-50 flex items-center justify-center">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    <span id="loginText">تسجيل الدخول</span>
                    <div id="loginSpinner" class="hidden">
                        <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    </div>
                </button>
            </form>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8">
            <p class="text-blue-100 text-sm">
                © 2024 نظام إدارة الصالة الرياضية. جميع الحقوق محفوظة.
            </p>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Password toggle functionality
        const togglePassword = document.getElementById('togglePassword');
        const passwordInput = document.getElementById('password');
        const eyeIcon = document.getElementById('eyeIcon');

        togglePassword.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            if (type === 'password') {
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            } else {
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            }
        });

        // Form validation and submission
        const loginForm = document.getElementById('loginForm');
        const usernameInput = document.getElementById('username');
        const usernameError = document.getElementById('username-error');
        const passwordError = document.getElementById('password-error');
        const loginText = document.getElementById('loginText');
        const loginSpinner = document.getElementById('loginSpinner');

        // Real-time validation
        usernameInput.addEventListener('blur', function() {
            if (!this.value.trim()) {
                showError(usernameError, 'يرجى إدخال اسم المستخدم');
                this.classList.add('border-red-500');
            } else {
                hideError(usernameError);
                this.classList.remove('border-red-500');
                this.classList.add('border-green-500');
            }
        });

        passwordInput.addEventListener('blur', function() {
            if (!this.value.trim()) {
                showError(passwordError, 'يرجى إدخال كلمة المرور');
                this.classList.add('border-red-500');
            } else if (this.value.length < 3) {
                showError(passwordError, 'كلمة المرور قصيرة جداً');
                this.classList.add('border-red-500');
            } else {
                hideError(passwordError);
                this.classList.remove('border-red-500');
                this.classList.add('border-green-500');
            }
        });

        // Form submission
        loginForm.addEventListener('submit', function(e) {
            let isValid = true;

            // Validate username
            if (!usernameInput.value.trim()) {
                showError(usernameError, 'يرجى إدخال اسم المستخدم');
                usernameInput.classList.add('border-red-500');
                isValid = false;
            }

            // Validate password
            if (!passwordInput.value.trim()) {
                showError(passwordError, 'يرجى إدخال كلمة المرور');
                passwordInput.classList.add('border-red-500');
                isValid = false;
            }

            if (!isValid) {
                e.preventDefault();
                return;
            }

            // Show loading state
            loginText.classList.add('hidden');
            loginSpinner.classList.remove('hidden');

            // Disable form elements
            const formElements = loginForm.querySelectorAll('input, button');
            formElements.forEach(element => {
                element.disabled = true;
            });
        });

        // Helper functions
        function showError(errorElement, message) {
            errorElement.textContent = message;
            errorElement.classList.remove('hidden');
        }

        function hideError(errorElement) {
            errorElement.classList.add('hidden');
        }

        // Auto-hide error messages after 5 seconds
        document.querySelectorAll('.bg-red-50').forEach(alert => {
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 300);
            }, 5000);
        });

        // Focus on username input when page loads
        window.addEventListener('load', function() {
            usernameInput.focus();
        });

        // Handle Enter key navigation
        usernameInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                passwordInput.focus();
            }
        });

        // Add floating label effect
        const inputs = document.querySelectorAll('input[type="text"], input[type="password"]');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
        });
    </script>
</body>
</html>


