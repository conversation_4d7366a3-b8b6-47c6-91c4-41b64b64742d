{% extends "layout.html" %}
{% block title %}تسجيل الدخول - بيت الأنبا كاراس لذوي الهمم{% endblock %}
{% block content %}

<div class="login-container">

    <img src="{{ url_for('static', filename='logo.png') }}" alt="شعار" style="width: 80px; margin-bottom: 15px;">

    <h2>مرحبًا بك!</h2>
    <p>من فضلك سجل دخولك</p>

    <!--لإظهار رسالة الخطأ في نفس شاشة الدخول-->
    {% with messages = get_flashed_messages(category_filter=['error']) %}
    {% if messages %}
        <div class="login-error">
        {% for message in messages %}
            {{ message }}
        {% endfor %}
        </div>
    {% endif %}
    {% endwith %}


    <form method="post">
        <div class="input-group">
            <label for="username">اسم المستخدم:</label>
            <input type="text" id="username" name="username" required>
        </div>

        <div class="input-group">
            <label for="password">كلمة المرور:</label>
            <input type="password" id="password" name="password" required>
        </div>

        <button type="submit" class="btn-login">🔐 دخول</button>
    </form>
</div>

<style>
/* الخلفية */
body {
    background-color: #f0f2f5 !important;
    background-image: url("{{ url_for('static', filename='logo.png') }}") !important;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    background-attachment: fixed;

    /* ✅ لإزالة تأثير الهيدر والفوتر */
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

/* ✅ إزالة padding من main الخاص بالـ layout */
main {
    background-color: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    width: 100%;
}

/* صندوق الدخول */
.login-container {
    width: 100%;
    max-width: 500px;  /* ✅ زوّد العرض هنا */
    padding: 40px 35px;
    background-color: rgba(255, 255, 255, 0.35);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
    text-align: center;
    font-family: 'Tajawal', sans-serif;
    color: #000;
    background-color: rgba(255, 255, 255, 0.5); /* بدلاً من 0.35 */
    backdrop-filter: blur(12px);
}

.login-error {
    background-color: #ffe6e6;
    color: #cc0000;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
    padding: 10px 15px;
    border-radius: 6px;
    font-size: 15px;
}

.login-container h2 {
    color: #02101f;
    margin-bottom: 8px;
}

.input-group {
    text-align: right;
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: bold;
    color: #333;
}

.input-group input[type="text"],
.input-group input[type="password"] {
    width: 100%;
    padding: 12px 14px;
    border: 1px solid #ccc;
    border-radius: 6px;
    direction: rtl;
    text-align: right;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.input-group input:focus {
    border-color: #007BFF;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
    outline: none;
}

.btn-login {
    background-color: #007BFF;
    color: white;
    border: none;
    padding: 14px 20px;
    font-size: 16px;
    font-weight: bold;
    border-radius: 6px;
    width: 100%;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-login:hover {
    background-color: #0056b3;
}

body {
    height: 100vh;
    padding-top: var(--bar-height);   /* يمنع تغطية الهيدر */
    padding-bottom: var(--bar-height); /* يمنع تغطية الفوتر */
    box-sizing: border-box;
}

main {
    display: flex;
    align-items: center;
    justify-content: center;
    height: calc(100vh - (var(--bar-height) * 2));
    padding: 0;
    margin: 0;
    background: transparent;
}


</style>



{% endblock %}
